import { PREVIEW_IMAGE_STYLES } from "@/constants/chatConstants";
import { Image, CloseButton } from "react-bootstrap";

const ImagePreviewSection = ({
  imagePreview,
  attachedImage,
  onClose,
}: {
  imagePreview: string | null;
  attachedImage: File | null;
  onClose: () => void;
}) => {
  if (!imagePreview) return null;

  return (
    <div className="image-preview-section p-3 border-top">
      <div className="d-flex align-items-center gap-2">
        <div className="position-relative">
          <Image
            src={imagePreview}
            alt="Preview"
            style={PREVIEW_IMAGE_STYLES}
          />
          <CloseButton
            variant="black"
            className="close-btn position-absolute top-0 end-0 m-1"
            onClick={onClose}
          />
        </div>
        <div className="flex-grow-1">
          <small className="text-muted">
            {attachedImage?.name} (
            {((attachedImage?.size || 0) / 1024 / 1024).toFixed(2)} MB)
          </small>
        </div>
      </div>
    </div>
  );
};

export default ImagePreviewSection;
