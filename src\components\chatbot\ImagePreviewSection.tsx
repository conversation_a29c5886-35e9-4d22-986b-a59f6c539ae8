import { Image } from "react-bootstrap";

const ImagePreviewSection = ({
  imagePreview,
  attachedImage,
}: {
  imagePreview: string | null;
  attachedImage: File | null;
}) => {
  if (!imagePreview) return null;

  return (
    <div className="image-preview-section p-3 border-top">
      <div className="d-flex align-items-center gap-2">
        <div className="position-relative">
          <Image
            src={imagePreview}
            alt="Preview"
            style={{
              maxWidth: "100px",
              maxHeight: "100px",
              objectFit: "cover",
              borderRadius: "8px",
            }}
          />
        </div>
        <div className="flex-grow-1">
          <small className="text-muted">
            {attachedImage?.name} (
            {((attachedImage?.size || 0) / 1024 / 1024).toFixed(2)} MB)
          </small>
        </div>
      </div>
    </div>
  );
};

export default ImagePreviewSection;
