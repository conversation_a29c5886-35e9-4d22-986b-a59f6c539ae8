import { INPUT_STYLES } from "@/constants/chatConstants";
import { ACCEPTED_IMAGE_TYPES } from "@/utils/imageValidation";
import { AttachSquare, Gift, Happyemoji, Send } from "iconsax-react";
import { Button, Form, InputGroup } from "react-bootstrap";
import Smiley<PERSON>ropdown from "./SmileyDropdown";

const InputActions = ({
  handleSend,
  handleAttachClick,
  handleFileChange,
  handleGiftClick,
  handleEmojiClick,
  handleEmojiSelect,
  handleKeyDown,
  input,
  setInput,
  inputRef,
  emojiDropdownRef,
  showEmojiDropdown,
  smileys,
  attachedImage,
  fileInputRef,
  isConnected,
}: any) => {
  return (
    <div className="chatpopup-footer">
      <Form
        onSubmit={(e) => {
          e.preventDefault();
          handleSend();
        }}
      >
        <InputGroup className="gap-3 align-items-center">
          {/* <Form.Control
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter Message"
            /> */}

          <div
            ref={inputRef}
            contentEditable
            suppressContentEditableWarning
            role="textbox"
            aria-multiline="true"
            onInput={(e) => {
              setInput(e.currentTarget.innerHTML);
            }}
            onKeyDown={handleKeyDown}
            className="form-control"
            data-placeholder="Enter Message"
            style={INPUT_STYLES}
          />
          <div className="d-flex align-items-center gap-3">
            <Button
              className="icons-btn"
              title="Attach File"
              onClick={handleAttachClick}
            >
              <AttachSquare size="24" color="#000000" />
            </Button>
            <Form.Control
              type="file"
              accept={ACCEPTED_IMAGE_TYPES.join(",")}
              onChange={handleFileChange}
              ref={fileInputRef}
              style={{ display: "none" }}
            />
            <Button
              className="icons-btn"
              onClick={handleGiftClick}
              title="Send Gift"
            >
              <Gift size="24" color="#000000" variant="Outline" />
            </Button>
            <div className="position-relative" ref={emojiDropdownRef}>
              <Button
                className="icons-btn"
                onClick={handleEmojiClick}
                title="Add Emoji"
              >
                <Happyemoji size="24" color="#000000" variant="Outline" />
              </Button>
              {showEmojiDropdown && (
                <SmileyDropdown
                  smileys={smileys}
                  handleEmojiSelect={handleEmojiSelect}
                />
              )}
            </div>
            <Button
              variant="primary"
              className="send-btn"
              onClick={handleSend}
              disabled={!isConnected || (!input.trim() && !attachedImage)}
              title={
                !isConnected
                  ? "Connecting..."
                  : attachedImage
                    ? "Send image"
                    : "Send message"
              }
            >
              <Send size="24" color="#ffffff" variant="Bold" />
            </Button>
          </div>
        </InputGroup>
      </Form>
    </div>
  );
};

export default InputActions;
