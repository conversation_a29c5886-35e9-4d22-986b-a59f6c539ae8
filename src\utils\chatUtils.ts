import { EMOJI_STYLES } from "@/constants/chatConstants";
import { MessageType } from "@/types";
import { FormatS3ImgUrl } from "@/utils";

// Types
export interface TempMessage {
  id: string;
  receiverId: number;
  message: string;
  messageType: MessageType;
  timestamp: string;
  status: "uploading";
  conversationId: number;
  sender: "me";
  isTemp: true;
  tempId: string;
}

/**
 * Creates a temporary message object for displaying during image upload
 */
export const createTempMessage = (
  tempMessageId: string,
  activeChatUserId: number,
  imagePreview: string
): TempMessage => ({
  id: tempMessageId,
  receiverId: activeChatUserId,
  message: imagePreview || "",
  messageType: MessageType.IMAGE,
  timestamp: new Date().toISOString(),
  status: "uploading" as const,
  conversationId: 0,
  sender: "me" as const,
  isTemp: true,
  tempId: tempMessageId,
});

/**
 * Removes a temporary message from the state
 */
export const removeTempMessage = (
  setTempMessages: React.Dispatch<React.SetStateAction<Record<string, any>>>,
  tempMessageId: string
) => {
  setTempMessages((prev) => {
    const newState = { ...prev };
    delete newState[tempMessageId];
    return newState;
  });
};

/**
 * Formats date for message grouping display
 */
export const getDateLabel = (dateString: string): string => {
  const today = new Date();
  const msgDate = new Date(dateString);
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  if (msgDate.toDateString() === today.toDateString()) return "Today";
  if (msgDate.toDateString() === yesterday.toDateString()) return "Yesterday";

  return msgDate.toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });
};

/**
 * Inserts an emoji into the contentEditable input element
 */
export const insertEmojiIntoInput = (
  inputRef: React.RefObject<HTMLDivElement | null>,
  emoji: any
) => {
  if (!inputRef.current) return;

  const img = document.createElement("img");
  img.src = FormatS3ImgUrl(emoji.image);
  img.alt = "Emoji";
  img.style.maxWidth = EMOJI_STYLES.maxWidth;
  img.style.height = EMOJI_STYLES.height;

  inputRef.current.focus();

  const selection = window.getSelection();
  if (selection?.rangeCount) {
    const range = selection.getRangeAt(0);
    range.insertNode(img);
    range.setStartAfter(img);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
  } else {
    inputRef.current.appendChild(img);
  }

  return inputRef.current.innerHTML;
};

/**
 * Groups messages by date for display
 */
export const groupMessagesByDate = (messages: any[]) => {
  const groups: Record<string, typeof messages> = {};

  messages.forEach((msg) => {
    const date = new Date(msg.timestamp).toISOString().split("T")[0];

    if (!groups[date]) groups[date] = [];
    groups[date].push(msg);
  });

  return groups;
};

/**
 * Combines real messages with temporary messages and sorts them by timestamp
 */
export const combineAndSortMessages = (
  baseMessages: any[],
  tempMessages: Record<string, any>
) => {
  const tempMessagesArray = Object.values(tempMessages);
  
  return [...baseMessages, ...tempMessagesArray].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
};
