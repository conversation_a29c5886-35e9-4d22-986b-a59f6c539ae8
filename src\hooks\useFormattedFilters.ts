import { useMemo } from "react";

type FilterFormat = "raw" | "array" | "id" | "commaString";

const filterFormatMap: Record<FilterFormat, string[]> = {
    raw: ["name", "withPhotos"],
    commaString: [
        "ageRange",
        "distance",
        "interestIds",
        "relationshipStatusId",
        "hairColorId",
        "bestFeatureId",
        "eyeColorId",
        "personalityId",
        "appearanceId",
        "bodyTypeId",
        "smokingHabitId",
        "drinkingHabitId",
    ],
    array: [],
    id: [],
};

const getFormatType = (key: string): FilterFormat | undefined => {
    for (const format in filterFormatMap) {
        if (
            filterFormatMap[format as FilterFormat].includes(key)
        ) {
            return format as FilterFormat;
        }
    }
    return undefined;
};

const transformFilterValue = (key: string, value: any): any => {
    const format = getFormatType(key);

    switch (format) {
        case "raw":
            return value;
        case "commaString":
            return Array.isArray(value) ? value.map((v) => v?.value).join(",") : "";
        case "array":
            return Array.isArray(value) ? value.map((v) => v?.value) : [];
        case "id":
            return value?.id ?? undefined;
        default:
            return value;
    }
};

const useFormattedFilters = (filters: Record<string, any>) => {
    const formatted = useMemo(() => {
        const params: Record<string, any> = {};

        Object.entries(filters).forEach(([key, value]) => {
            const transformed = transformFilterValue(key, value);
            if (
                transformed !== undefined &&
                transformed !== "" &&
                !(Array.isArray(transformed) && transformed.length === 0)
            ) {
                params[key] = transformed;
            }
        });

        return params;
    }, [filters]);

    return formatted;
};

export default useFormattedFilters;