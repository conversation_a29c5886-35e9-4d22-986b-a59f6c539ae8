@use '@/variables' as *;

.plan-card {
    border-radius: 32px;
    background: $white-color;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.10);

    // &.active, &:hover {
    //     background: var(--Button, linear-gradient(270deg, #B936AD 0%, #79259C 100%));

    //     .plan-card {
    //         &-detail {
    //             .icon-wrapper {
    //                 background: $white-color;

    //                 svg path {
    //                     fill: #B936AD;
    //                 }
    //             }

    //             .coins-number {
    //                 color: $white-color;
    //             }

    //             .coins-title {
    //                 color: $white-color;
    //             }
    //         }
    //     }

    //     .original-price,
    //     .current-price {
    //         color: $white-color;
    //     }

    //     .buy-btn {
    //         background: $white-color;
    //         color: $black-color;
    //     }
    // }

    .card-header {
        background: linear-gradient(255deg, rgba(185, 54, 173, 0.05) -44.9%, #000 23.09%, #000 72.97%, #B936AD 159.5%);
        border-radius: 32px 32px 0px 0px;
        height: 160px;

        .title {
            color: $white-color;
            font-size: 28px;
            font-weight: 600;
            line-height: normal;
        }
    }

    &-content {
        padding: 0px 32px 32px 32px;
    }

    &-detail {
        margin-top: 32px;
        border-bottom: 1px solid rgba(20, 20, 20, 0.1);
        padding-bottom: 26px;
        margin-bottom: 26px;


        .icon-wrapper {
            border: 1px solid #EFEFEF;
            width: 82px;
            height: 82px;
            min-width: 82px;
            border-radius: 100%;
        }

        .coins-number {
            color: $black-color;
            font-size: 45px;
            font-weight: 700;
            line-height: normal;
        }

        .coins-title {
            color: $text-color;
            font-size: 20px;
            font-weight: 600;
            line-height: normal;
        }
    }

    .original-price {
        color: $text-color;
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
    }

    .current-price {
        color: $black-color;
        font-size: 32px;
        font-weight: 700;
        line-height: normal;
    }

    .buy-btn {
        border-radius: 8px;
        background: var(--Button, linear-gradient(270deg, #B936AD 0%, #79259C 100%));
        padding: 16px 30px;
        color: $white-color;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.5;
        border: none;
    }
}

@media(max-width:991px) {
    .plan-card {
        .card-header {
            height: 100px;

            .title {
                font-size: 24px;
            }
        }

        &-content {
            padding: 0px 24px 24px 24px;
        }

        &-detail {
            margin-top: 24px;
            padding-bottom: 16px;
            margin-bottom: 16px;

            .icon-wrapper {
                width: 62px;
                height: 62px;
                min-width: 62px;

                svg {
                    width: 32px;
                }
            }

            .coins-number {
                font-size: 32px;
            }

            .coins-title {
                font-size: 16px;
            }


        }

        .original-price {
            font-size: 16px;
        }

        .current-price {
            font-size: 24px;
        }
    }
}