export interface SuccessPageContent {
  [key: string]: {
    heading: string;
    subheading: string;
    description: string;
    useIcon: boolean;
    imagePath?: string;
    btnText?: string;
    redirectURL?: string;
    hideBtn?: boolean;
  };
}

export interface NotificationItem {
  id: string;
  created_at: string;
  notification: {
    title: string;
    message: string;
  };
}

export interface ConfirmModalConfig {
  visible: boolean;
  data: {
    onSubmit?: () => void;
    onClose?: () => void;
    content?: {
      heading: string;
      description: string;
    };
    icon?: any;
    iconColor?: string;
    showCloseIcon?: boolean;
    buttonText?: string;
    showModalIcon?: boolean;
    customStyling?: any;
  };
}

export interface PaginationInterface {
  page: number;
  limit: number;
}

export interface PlanInterface {
  id: number;
  title: string;
  heading_one: string;
  heading_two: string;
  description: string;
  amount: number;
}

export enum VerifyAccountTypeEnum {
  MEMBER = "MEMBER",
  OFFLINE = "OFFLINE",
}

export type IDType = number | string;

export interface PhotoItem {
  id: string;
  image: string;
  filename?: string;
}

export enum MessageType {
  TEXT = "text",
  GIFT = "gift",
}
