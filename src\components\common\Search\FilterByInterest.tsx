import { FormatS3ImgUrl } from "@/utils";
import { autoUpdate, offset, shift, useFloating } from "@floating-ui/react-dom";
import { Add } from "iconsax-react";
import { useEffect, useState } from "react";
import { Button, Image } from "react-bootstrap";

interface InterestItem {
  value: string | number;
  label: string;
  image: string;
}

interface FilterByInterestProps {
  interestData: InterestItem[];
  selected: InterestItem[];
  setSelected: (items: InterestItem[]) => void;
  toggleSelect: (item: InterestItem) => void;
  onSubmit: (selected: InterestItem[]) => void;
}

const FilterByInterest = ({
  interestData,
  selected,
  setSelected,
  toggleSelect,
  onSubmit,
}: FilterByInterestProps) => {
  const [open, setOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const onClickSearch = () => {
    setOpen(false);
    onSubmit(selected);
  };

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  const { refs, floatingStyles, update } = useFloating({
    open,
    middleware: [offset(5), shift({ padding: 8 })],
    strategy: "fixed",
    whileElementsMounted: autoUpdate,
    placement: "bottom",
  });

  // Toggle dropdown
  const onToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpen(!open);

    // Update position when opening
    if (!open) {
      setTimeout(update, 0);
    }
  };

  const isSelected = (item: InterestItem) =>
    selected?.find((i) => i?.value === item?.value);

  // Handle click outside
  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as Node;
    const referenceEl = refs.reference.current as Element | null;
    const floatingEl = refs.floating.current as Element | null;

    if (
      open &&
      referenceEl &&
      floatingEl &&
      !referenceEl.contains(target) &&
      !floatingEl.contains(target)
    ) {
      setOpen(false);
    }
  };

  // Add event listener for click outside
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);

    // Add scroll event listener to update position when scrolling
    if (open) {
      const handleScroll = () => {
        requestAnimationFrame(update);
      };

      window.addEventListener("scroll", handleScroll, true);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        window.removeEventListener("scroll", handleScroll, true);
      };
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, update]);

  return (
    <div className="custom-interest-dropdown">
      <button
        ref={refs.setReference}
        className={`custom-dropdown interest ${open ? "active" : ""}`}
        onClick={onToggle}
      >
        Interests
      </button>

      {open && (
        <div
          ref={refs.setFloating}
          className={`custom-interest-dropdown-menu show ${isMobile ? "mobile-view" : ""}`}
          onClick={(e) => e.stopPropagation()}
          style={{
            ...(isMobile
              ? {
                  position: "fixed",
                  top: "0",
                  left: "0",
                  right: "0",
                  width: "100%",
                  height: "100vh",
                  zIndex: 1050,
                  background: "#fff",
                }
              : {
                  ...floatingStyles,
                  width: "100vw",
                  left: "0",
                  right: "0",
                  position: "fixed",
                  zIndex: 1050,
                }),
          }}
        >
          {isMobile && (
            <div className="mobile-header">
              <h5>Select Interests</h5>
              <button
                className="close-button"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpen(false);
                }}
              >
                <Add
                  size="24"
                  style={{ transform: "rotate(45deg)" }}
                  color="#000"
                />
              </button>
            </div>
          )}
          <div className="custom-interest-menu-content container-fluid">
            <div className="custom-interest-menu-data">
              {interestData.map((item: InterestItem) => (
                <div key={item.value}>
                  <div
                    className={`interest-card ${isSelected(item) ? "selected" : ""}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleSelect(item);
                    }}
                  >
                    <Image
                      src={FormatS3ImgUrl(item.image)}
                      className="interest-img"
                    />
                    <div className="label-overlay">{item.label}</div>
                    <div className="checkmark"></div>
                  </div>
                </div>
              ))}
            </div>
            <div className="d-flex align-items-center gap-3 mt-3 justify-content-end">
              <Button
                variant="primary"
                onClick={() => setSelected([])}
                style={{ minWidth: "100px" }}
              >
                Clear
              </Button>
              <Button variant="primary" onClick={onClickSearch} style={{ minWidth: "100px" }}>
                Confirm
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterByInterest;
