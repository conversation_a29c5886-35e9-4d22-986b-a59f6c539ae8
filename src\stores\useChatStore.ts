import io from "socket.io-client";
import { create } from "zustand";
import useUserS<PERSON>, { getAuthToken, getUser<PERSON>oins, setUserCoins } from "./user";
import { MessageType } from "@/types";
import toast from "react-hot-toast";

// User/Sender interface based on API response
interface User {
  id: number;
  name: string | null;
  username: string;
  email: string | null;
  city: string;
  avatar: string | null;
  isOnline: boolean;
  createdAt: string;
  updatedAt: string;
}

type messageType = MessageType;

// Chat user interface for UI components
interface ChatUser {
  id: number;
  name: string;
  location: string;
  avatar?: string;
}

// Message interface based on API response
interface ApiMessage {
  id: number;
  sender: User;
  senderId: number;
  conversationId: number;
  message: string;
  messageType: messageType;
  messageStatus: "sent" | "delivered" | "read";
  isRead: boolean;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}

// Simplified message interface for UI
interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  message: string;
  messageType: messageType;
  timestamp: string;
  status: "sent" | "delivered" | "read";
  conversationId: number;
  sender?: "me" | "other"; // Computed field for UI
}

// UI Conversation interface with user details
interface Conversation {
  id: number;
  customerId: number;
  modelId: number;
  participants: number[];
  name?: string;
  username?: string;
  avatar?: string;
  location?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  isOnline?: boolean;
}

interface ChatState {
  activeChatUser: ChatUser | null;
  messages: Record<number, Message[]>;
  conversations: Conversation[];
  socket: any;
  isConnected: boolean;
  currentUserId: number | null;

  // Actions
  setActiveChatUser: (user: ChatUser | null) => void;
  sendMessage: (
    toUserId: number,
    message: string,
    messageType?: messageType
  ) => void;
  receiveMessage: (messageData: Message) => void;
  initializeSocket: () => () => void;
  disconnectSocket: () => void;
  getConversations: (page?: number, limit?: number) => void;
  getChatMessages: (otherUserId: number, page?: number, limit?: number) => void;
  setMessages: (userId: number, messages: Message[]) => void;
  setConversations: (conversations: Conversation[]) => void;

  // Helper functions
  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ) => Message;
  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ) => Message[];
  processConversations: (
    conversations: any[],
    currentUserId: number
  ) => Conversation[];
}

const useChatStore = create<ChatState>((set, get) => ({
  activeChatUser: null,
  messages: {},
  conversations: [],
  socket: null,
  isConnected: false,
  currentUserId: null,

  // Helper functions
  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ): Message => {
    return {
      id: apiMessage.id,
      senderId: apiMessage.senderId,
      receiverId:
        currentUserId === apiMessage.senderId
          ? apiMessage.conversationId
            ? 0
            : currentUserId
          : currentUserId, // Will be updated with proper logic
      message: apiMessage.message,
      messageType: apiMessage.messageType,
      timestamp: apiMessage.createdAt,
      status: apiMessage.messageStatus,
      conversationId: apiMessage.conversationId,
      sender: apiMessage.senderId === currentUserId ? "me" : "other",
    };
  },

  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ): Message[] => {
    return apiMessages.map((apiMessage) =>
      get().transformApiMessage(apiMessage, currentUserId)
    );
  },

  processConversations: (
    conversations: any[],
    currentUserId: number
  ): Conversation[] => {
    // This method will process raw conversation data and enrich it with user details
    // For now, return as-is, but this can be enhanced based on your API structure
    return conversations.map((conv) => ({
      id: conv.id,
      customerId: conv.customerId || currentUserId,
      modelId: conv.modelId,
      participants: conv.participants || [],
      name: conv.name || conv.username || `User ${conv.id}`,
      username: conv.username,
      avatar: conv.avatar,
      location: conv.location || conv.city,
      lastMessage: conv.lastMessage,
      lastMessageTime: conv.lastMessageTime,
      isOnline: conv.isOnline || false,
    }));
  },

  setActiveChatUser: (user) => {
    // console.log('Setting active chat user:', user);
    set({ activeChatUser: user });

    // Set current user ID if not already set
    const userInfo = useUserStore.getState().userInfo.user;
    if (userInfo?.id && !get().currentUserId) {
      set({ currentUserId: userInfo.id });
    }

    // Load chat messages when user is selected
    if (user && get().isConnected) {
      get().getChatMessages(user.id);
    }
  },

  sendMessage: (toUserId, message, messageType = MessageType.TEXT) => {
    set((state) => {
      const socket = state.socket;

      if (socket && state.isConnected && state.currentUserId) {
        const messageData = {
          receiverId: toUserId,
          message,
          messageType,
        };

        socket.emit("send_chat_message", messageData, (response: any) => {
          // console.log("Message sent response:", response);
          if (response?.success === false) {
            toast.error(response?.message || "Failed to send message");
            return;
          }

          const newMessage: Message = {
            id: Date.now(),
            senderId: state.currentUserId!,
            receiverId: toUserId,
            message,
            messageType,
            timestamp: new Date().toISOString(),
            status: "sent",
            conversationId: 0,
            sender: "me",
          };

          setUserCoins(
            getUserCoins().totalCoins,
            response?.remainingCoin
          );

          set((innerState) => ({
            messages: {
              ...innerState.messages,
              [toUserId]: [
                ...(innerState.messages[toUserId] || []),
                newMessage,
              ],
            },
          }));
        });
      }

      return state;
    });
  },

  receiveMessage: (messageData: Message) => {
    set((state) => {
      const senderId = messageData.senderId;
      const updatedMessage: Message = {
        ...messageData,
        sender: messageData.senderId === state.currentUserId ? "me" : "other",
      };

      return {
        messages: {
          ...state.messages,
          [senderId]: [...(state.messages[senderId] || []), updatedMessage],
        },
      };
    });
  },

  initializeSocket: () => {
    const token = getAuthToken();
    const userInfo = useUserStore.getState().userInfo.user;
    const userId = userInfo?.id;

    if (!token || !userId) {
      console.error("Cannot initialize socket: missing token or userId", {
        token: !!token,
        userId,
      });
      return () => {};
    }

    set({ currentUserId: userId });

    const socketUrl = import.meta.env.VITE_SOCKET_URL;

    if (!socketUrl) {
      console.error("Socket URL not configured in environment variables");
      return () => {};
    }

    const socket = io(socketUrl, {
      auth: {
        token,
        userId: parseInt(userId.toString()),
      },
      transports: ["websocket", "polling"],
      timeout: 20000, // 20 second timeout
    });

    // set({ socket, isConnected: false });

    socket.on("connect", () => {
      // console.log("Socket connected successfully");
      set({ isConnected: true, socket });
    });

    socket.on("disconnect", (reason) => {
      // console.log("Socket disconnected:", reason);
      set({ isConnected: false });
    });

    // Handle real-time message reception based on your API response
    socket.on("direct_message_received", (response) => {
      try {
        // console.log("Received message:", response);

        if (!response || !response.message) {
          console.error("Invalid message response format:", response);
          return;
        }

        const messageData = response.message;
        useChatStore.getState().receiveMessage(messageData);
      } catch (error) {
        console.error("Error processing received message:", error);
      }
    });

    socket.on("conversations", (data) => {
      try {
        // console.log("Received conversations:", data);

        if (!data) {
          console.error("No conversations data received");
          return;
        }

        // Handle conversations list - structure may vary based on your API
        const conversations =
          data.conversations || data.data?.conversations || data;

        if (!Array.isArray(conversations)) {
          console.error("Conversations data is not an array:", conversations);
          return;
        }

        useChatStore.getState().setConversations(conversations);
      } catch (error) {
        console.error("Error processing conversations:", error);
      }
    });

    socket.on("chat_messages", (response) => {
      try {
        // console.log("Received chat messages:", response);

        if (!response || !response.success || !response.data) {
          console.error("Invalid chat messages response format:", response);
          return;
        }

        const { messages, conversation } = response.data;
        const currentUserId = useChatStore.getState().currentUserId;

        if (!currentUserId) {
          console.error("Current user ID not set");
          return;
        }

        if (!Array.isArray(messages)) {
          console.error("Messages data is not an array:", messages);
          return;
        }

        if (!conversation || !Array.isArray(conversation.participants)) {
          console.error("Invalid conversation data:", conversation);
          return;
        }

        if (messages.length > 0) {
          // Transform API messages to our Message format
          const transformedMessages = useChatStore
            .getState()
            .processApiMessages(messages, currentUserId);

          // Determine the other user ID from the conversation
          const otherUserId = conversation.participants.find(
            (id: number) => id !== currentUserId
          );

          if (otherUserId) {
            useChatStore
              .getState()
              .setMessages(otherUserId, transformedMessages);
          } else {
            console.error(
              "Could not determine other user ID from participants:",
              conversation.participants
            );
          }
        }
      } catch (error) {
        console.error("Error processing chat messages:", error);
      }
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      set({ isConnected: false });
    });

    // Add additional event listeners for debugging
    socket.on("reconnect", (attemptNumber) => {
      // console.log("Socket reconnected after", attemptNumber, "attempts");
      set({ isConnected: true });
    });

    socket.on("reconnect_error", (error) => {
      console.error("Socket reconnection error:", error);
    });

    return () => {
      // console.log("Cleaning up socket connection");
      socket.disconnect();
      set({ socket: null, isConnected: false });
    };
  },

  disconnectSocket: () => {
    const { socket } = get();
    if (socket) {
      socket.disconnect();
      set({ socket: null, isConnected: false });
    }
  },

  getConversations: (page = 1, limit = 20) => {
    const { socket, isConnected } = get();

    if (!socket) {
      console.error("Cannot get conversations: Socket not initialized");
      return;
    }

    if (!isConnected) {
      console.error("Cannot get conversations: Socket not connected");
      return;
    }

    try {
      // console.log('Requesting conversations:', { page, limit });
      socket.emit("get_conversations", { page, limit }, (response: any) => {
        if (response?.success) {
          useChatStore.getState().setConversations(response.data.conversations);
        }
      });
    } catch (error) {
      console.error("Error requesting conversations:", error);
    }
  },

  getChatMessages: (otherUserId, page = 1, limit = 20) => {
    const { socket, isConnected } = get();

    if (!socket) {
      console.error("Cannot get chat messages: Socket not initialized");
      return;
    }

    if (!isConnected) {
      console.error("Cannot get chat messages: Socket not connected");
      return;
    }

    if (!otherUserId) {
      console.error("Cannot get chat messages: otherUserId is required");
      return;
    }

    try {
      // console.log('Requesting chat messages:', { otherUserId, page, limit });
      socket.emit("get_chat_messages", { otherUserId, page, limit });
    } catch (error) {
      console.error("Error requesting chat messages:", error);
    }
  },

  setMessages: (userId, messages) => {
    set((state) => ({
      messages: {
        ...state.messages,
        [userId]: messages,
      },
    }));
  },

  setConversations: (conversations) => {
    const currentUserId = get().currentUserId;
    if (currentUserId) {
      const processedConversations = get().processConversations(
        conversations,
        currentUserId
      );
      set({ conversations: processedConversations });
    } else {
      set({ conversations });
    }
  },
}));

export const cleanupChatSocket = () => {
  const { disconnectSocket, setActiveChatUser } = useChatStore.getState();
  setActiveChatUser(null);
  disconnectSocket();
};

export default useChatStore;
