import { useMaster } from "@/api/utils.api";
import defaultProfile from "@/assets/images/user.png";
import { useS3Upload } from "@/hooks/useS3Upload";
import useChatStore from "@/stores/useChatStore";
import { FormatS3ImgUrl } from "@/utils";
import { ACCEPTED_IMAGE_TYPES, validateImageFile } from "@/utils/imageValidation";
import parse from "html-react-parser";
import { AttachSquare, Gift, Happyemoji, Send } from "iconsax-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  Button,
  Card,
  CloseButton,
  Form,
  Image,
  InputGroup,
  Spinner,
} from "react-bootstrap";
import toast from "react-hot-toast";
import GiftsModal from "./GiftsModal";
import SmileyDropdown from "./SmileyDropdown";
import "./styles.scss";
import { MessageType } from "@/types";

const ChatPopup = () => {
  const {
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
  } = useChatStore();
  const [input, setInput] = useState("");
  const [showGiftsModal, setShowGiftsModal] = useState(false);
  const [showEmojiDropdown, setShowEmojiDropdown] = useState(false);
  const [attachedImage, setAttachedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const emojiDropdownRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading } = useS3Upload();

  const { data: master = {} } = useMaster();
  const { gifts = [], smiley: smileys = [] } = master;

  const currentMessages = useMemo(() => {
    return activeChatUser ? messages[activeChatUser.id] || [] : [];
  }, [activeChatUser, messages]);

  useEffect(() => {
    if (activeChatUser && isConnected) {
      getChatMessages(activeChatUser.id);
    }
  }, [activeChatUser, isConnected, getChatMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentMessages]);

  useEffect(() => {
    const editableElement = inputRef.current;
    if (!editableElement) return;
    if (input.trim().length > 0) {
      editableElement.setAttribute("data-has-value", "true");
    } else {
      editableElement.removeAttribute("data-has-value");
    }
  }, [input]);

  const handleSend = useCallback(async () => {
    if (!isConnected || !activeChatUser) return;

    // Handle image attachment
    if (attachedImage) {
      try {
        const uploadResult = await uploadFile(attachedImage, { location: "chat" });

        if (!uploadResult.success) {
          toast.error(uploadResult.error || "Failed to upload image");
          return;
        }

        const imageHtml = `<img src="${FormatS3ImgUrl(uploadResult.filename!)}" alt="Attached Image" style="max-width: 300px; height: auto; border-radius: 8px;" />`;
        sendMessage(activeChatUser.id, imageHtml, MessageType.IMAGE);

        // Clear attachment
        setAttachedImage(null);
        setImagePreview(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } catch (error) {
        toast.error("Failed to send image");
        console.error("Image upload error:", error);
      }
      return;
    }

    // Handle text message
    if (input.trim()) {
      const htmlContent = inputRef.current?.innerHTML || input;
      sendMessage(activeChatUser.id, htmlContent, MessageType.TEXT);
      setInput("");
      if (inputRef.current) {
        inputRef.current.innerHTML = "";
        inputRef.current.focus();
      }
    }
  }, [input, isConnected, activeChatUser, sendMessage, attachedImage, uploadFile]);

  const handleClose = useCallback(() => {
    setActiveChatUser(null);
  }, [setActiveChatUser]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  const handleGiftClick = useCallback(() => {
    setShowGiftsModal(true);
  }, []);

  const handleGiftSelect = useCallback(
    (gift: any) => {
      if (activeChatUser && isConnected) {
        const giftImageHtml = `<img src="${FormatS3ImgUrl(gift.image)}" alt="Gift" style="max-width: 100px; height: auto;" />`;
        sendMessage(activeChatUser.id, giftImageHtml, MessageType.GIFT);
        setShowGiftsModal(false);
      }
    },
    [activeChatUser, isConnected, sendMessage]
  );

  const handleEmojiClick = useCallback(() => {
    setShowEmojiDropdown(!showEmojiDropdown);
  }, [showEmojiDropdown]);

  const handleEmojiSelect = useCallback((emoji: any) => {
    if (!inputRef.current) return;

    const img = document.createElement("img");
    img.src = FormatS3ImgUrl(emoji.image);
    img.alt = "Emoji";
    img.style.maxWidth = "30px";
    img.style.height = "auto";

    inputRef.current.focus();

    const selection = window.getSelection();
    if (selection?.rangeCount) {
      const range = selection.getRangeAt(0);
      range.deleteContents(); // Replace any selected content
      range.insertNode(img);
      range.setStartAfter(img);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      // If no selection (e.g., not focused), append to the end
      inputRef.current.appendChild(img);
    }

    setInput(inputRef.current.innerHTML);
    setShowEmojiDropdown(false);
  }, []);

  const handleAttachClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const error = validateImageFile(file);
    if (error) {
      toast.error(error);
      return;
    }

    setAttachedImage(file);

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);
  }, []);

  const handleRemoveAttachment = useCallback(() => {
    setAttachedImage(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [imagePreview]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiDropdownRef.current &&
        !emojiDropdownRef.current.contains(event.target as Node)
      ) {
        setShowEmojiDropdown(false);
      }
    };

    if (showEmojiDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showEmojiDropdown]);

  // Cleanup image preview URL on unmount or when preview changes
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  const groupedMessages = useMemo(() => {
    const groups: Record<string, typeof currentMessages> = {};

    currentMessages.forEach((msg) => {
      const date = new Date(msg.timestamp).toISOString().split("T")[0];

      if (!groups[date]) groups[date] = [];
      groups[date].push(msg);
    });

    return groups;
  }, [currentMessages]);

  const getDateLabel = (dateString: string) => {
    const today = new Date();
    const msgDate = new Date(dateString);
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    if (msgDate.toDateString() === today.toDateString()) return "Today";
    if (msgDate.toDateString() === yesterday.toDateString()) return "Yesterday";

    return msgDate.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  if (!activeChatUser) return null;

  return (
    <Card className="position-fixed chatpopup">
      <div className="d-flex justify-content-between align-items-center chatpopup-header">
        <div className="d-flex gap-2 align-items-center">
          <Image
            src={
              activeChatUser?.avatar
                ? FormatS3ImgUrl(activeChatUser.avatar)
                : defaultProfile
            }
            alt={activeChatUser.name}
            className="user-img object-fit-cover"
          />
          <div className="d-flex flex-column gap-1">
            <h6 className="name mb-0">{activeChatUser.name}</h6>
            <p className="location mb-0">{activeChatUser.location}</p>
            {/* <div
              className={`connection-status small ${isConnected ? "text-success" : "text-danger"}`}
            >
              {isConnected ? "● Online" : "● Offline"}
            </div> */}
          </div>
        </div>
        <CloseButton
          variant="black"
          className="close-btn"
          onClick={handleClose}
        />
      </div>
      <div className="message-screen" style={{ flex: 1 }}>
        {Object.entries(groupedMessages).map(([date, msgs]) => (
          <div key={date}>
            <div className="text-center text-muted small my-2">
              {getDateLabel(date)}
            </div>
            {msgs.map((msg, idx) => (
              <div
                key={msg.id || idx}
                className={`message ${msg.sender === "me" ? "message-send ms-auto text-start" : "message-receive text-start me-auto"}`}
              >
                {parse(msg.message)}
                {/* {msg.timestamp && (
                  <div
                    className={`message-timestamp small text-end ${msg.sender === "me" ? "text-light" : "text-muted"}`}
                  >
                    {new Date(msg.timestamp).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </div>
                )} */}
              </div>
            ))}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Image Preview Section */}
      {imagePreview && (
        <div className="image-preview-section p-3 border-top">
          <div className="d-flex align-items-center gap-2">
            <div className="position-relative">
              <Image
                src={imagePreview}
                alt="Preview"
                style={{
                  maxWidth: "100px",
                  maxHeight: "100px",
                  objectFit: "cover",
                  borderRadius: "8px"
                }}
              />
              {/* <Button
                variant="danger"
                size="sm"
                className="position-absolute top-0 end-0"
                style={{
                  transform: "translate(50%, -50%)",
                  width: "20px",
                  height: "20px",
                  padding: "0",
                  fontSize: "12px",
                  borderRadius: "50%"
                }}
                onClick={handleRemoveAttachment}
              >
                ×
              </Button> */}
            </div>
            <div className="flex-grow-1">
              <small className="text-muted">
                {attachedImage?.name} ({((attachedImage?.size || 0) / 1024 / 1024).toFixed(2)} MB)
              </small>
            </div>
          </div>
        </div>
      )}

      <div className="chatpopup-footer">
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSend();
          }}
        >
          <InputGroup className="gap-3 align-items-center">
            {/* <Form.Control
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter Message"
            /> */}

            <div
              ref={inputRef}
              contentEditable
              suppressContentEditableWarning
              role="textbox"
              aria-multiline="true"
              onInput={(e) => {
                setInput(e.currentTarget.innerHTML);
              }}
              onKeyDown={handleKeyDown}
              className="form-control"
              data-placeholder="Enter Message"
              style={{
                height: "max-content",
                maxHeight: "57px",
                overflowY: "auto",
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                outline: "none",
              }}
            />
            <div className="d-flex align-items-center gap-3">
              <Button
                className="icons-btn"
                title="Attach File"
                onClick={handleAttachClick}
                disabled={isUploading}
              >
                {isUploading ? (
                  <Spinner animation="border" size="sm" />
                ) : (
                  <AttachSquare size="24" color="#000000" />
                )}
              </Button>
              <Form.Control
                type="file"
                accept={ACCEPTED_IMAGE_TYPES.join(",")}
                onChange={handleFileChange}
                ref={fileInputRef}
                style={{ display: "none" }}
              />
              <Button
                className="icons-btn"
                onClick={handleGiftClick}
                title="Send Gift"
              >
                <Gift size="24" color="#000000" variant="Outline" />
              </Button>
              <div className="position-relative" ref={emojiDropdownRef}>
                <Button
                  className="icons-btn"
                  onClick={handleEmojiClick}
                  title="Add Emoji"
                >
                  <Happyemoji size="24" color="#000000" variant="Outline" />
                </Button>
                {showEmojiDropdown && (
                  <SmileyDropdown
                    smileys={smileys}
                    handleEmojiSelect={handleEmojiSelect}
                  />
                )}
              </div>
              <Button
                variant="primary"
                className="send-btn"
                onClick={handleSend}
                disabled={!isConnected || (!input.trim() && !attachedImage) || isUploading}
                title={
                  !isConnected
                    ? "Connecting..."
                    : isUploading
                    ? "Uploading..."
                    : attachedImage
                    ? "Send image"
                    : "Send message"
                }
              >
                {isUploading ? (
                  <Spinner animation="border" size="sm" />
                ) : (
                  <Send size="24" color="#ffffff" variant="Bold" />
                )}
              </Button>
            </div>
          </InputGroup>
        </Form>
      </div>

      <GiftsModal
        showGiftsModal={showGiftsModal}
        setShowGiftsModal={setShowGiftsModal}
        handleGiftSelect={handleGiftSelect}
        gifts={gifts}
      />
    </Card>
  );
};

export default ChatPopup;
