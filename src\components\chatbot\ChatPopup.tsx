import { useMaster } from "@/api/utils.api";
import defaultProfile from "@/assets/images/user.png";
import { useS3Upload } from "@/hooks/useS3Upload";
import useChatStore from "@/stores/useChatStore";
import { MessageType } from "@/types";
import { FormatS3ImgUrl } from "@/utils";
import { validateImageFile } from "@/utils/imageValidation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Card, CloseButton, Image } from "react-bootstrap";
import toast from "react-hot-toast";
import GiftsModal from "./GiftsModal";
import ImagePreviewSection from "./ImagePreviewSection";
import InputActions from "./InputActions";
import MessageItem from "./MessageItem";
import "./styles.scss";

// Types
interface TempMessage {
  id: string;
  receiverId: number;
  message: string;
  messageType: MessageType;
  timestamp: string;
  status: "uploading";
  conversationId: number;
  sender: "me";
  isTemp: true;
  tempId: string;
}

// Utility Functions
const createTempMessage = (
  tempMessageId: string,
  activeChatUserId: number,
  imagePreview: string
): TempMessage => ({
  id: tempMessageId,
  receiverId: activeChatUserId,
  message: imagePreview || "",
  messageType: MessageType.IMAGE,
  timestamp: new Date().toISOString(),
  status: "uploading" as const,
  conversationId: 0,
  sender: "me" as const,
  isTemp: true,
  tempId: tempMessageId,
});

const removeTempMessage = (
  setTempMessages: React.Dispatch<React.SetStateAction<Record<string, any>>>,
  tempMessageId: string
) => {
  setTempMessages((prev) => {
    const newState = { ...prev };
    delete newState[tempMessageId];
    return newState;
  });
};

const getDateLabel = (dateString: string): string => {
  const today = new Date();
  const msgDate = new Date(dateString);
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  if (msgDate.toDateString() === today.toDateString()) return "Today";
  if (msgDate.toDateString() === yesterday.toDateString()) return "Yesterday";

  return msgDate.toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });
};

const insertEmojiIntoInput = (
  inputRef: React.RefObject<HTMLDivElement | null>,
  emoji: any
) => {
  if (!inputRef.current) return;

  const img = document.createElement("img");
  img.src = FormatS3ImgUrl(emoji.image);
  img.alt = "Emoji";
  img.style.maxWidth = "30px";
  img.style.height = "auto";

  inputRef.current.focus();

  const selection = window.getSelection();
  if (selection?.rangeCount) {
    const range = selection.getRangeAt(0);
    range.insertNode(img);
    range.setStartAfter(img);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
  } else {
    inputRef.current.appendChild(img);
  }

  return inputRef.current.innerHTML;
};

// Custom Hooks
const useImageAttachment = () => {
  const [attachedImage, setAttachedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleAttachClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      const error = validateImageFile(file);
      if (error) {
        toast.error(error);
        return;
      }

      setAttachedImage(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    },
    []
  );

  const handleRemoveAttachment = useCallback(() => {
    setAttachedImage(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [imagePreview]);

  const clearAttachment = useCallback(() => {
    setAttachedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, []);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  return {
    attachedImage,
    imagePreview,
    fileInputRef,
    handleAttachClick,
    handleFileChange,
    handleRemoveAttachment,
    clearAttachment,
  };
};

const ChatPopup = () => {
  // Store hooks
  const {
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
  } = useChatStore();

  // API hooks
  const { uploadFile } = useS3Upload();
  const { data: master = {} } = useMaster();
  const { gifts = [], smiley: smileys = [] } = master;

  // Custom hooks
  const {
    attachedImage,
    imagePreview,
    fileInputRef,
    handleAttachClick,
    handleFileChange,
    handleRemoveAttachment,
    clearAttachment,
  } = useImageAttachment();

  // Local state
  const [input, setInput] = useState("");
  const [showGiftsModal, setShowGiftsModal] = useState(false);
  const [showEmojiDropdown, setShowEmojiDropdown] = useState(false);
  const [tempMessages, setTempMessages] = useState<Record<string, any>>({});

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const emojiDropdownRef = useRef<HTMLDivElement>(null);

  const currentMessages = useMemo(() => {
    const baseMessages = activeChatUser
      ? messages[activeChatUser.id] || []
      : [];
    const tempMessagesArray = Object.values(tempMessages);

    return [...baseMessages, ...tempMessagesArray].sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }, [activeChatUser, messages, tempMessages]);

  useEffect(() => {
    if (activeChatUser && isConnected) {
      getChatMessages(activeChatUser.id);
    }
  }, [activeChatUser, isConnected, getChatMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentMessages]);

  useEffect(() => {
    const editableElement = inputRef.current;
    if (!editableElement) return;
    if (input.trim().length > 0) {
      editableElement.setAttribute("data-has-value", "true");
    } else {
      editableElement.removeAttribute("data-has-value");
    }
  }, [input]);

  const handleSend = useCallback(async () => {
    if (!isConnected || !activeChatUser) return;

    if (attachedImage) {
      const tempMessageId = `temp-${Date.now()}`;
      const tempMessage = createTempMessage(
        tempMessageId,
        activeChatUser.id,
        imagePreview || ""
      );

      setTempMessages((prev) => ({
        ...prev,
        [tempMessageId]: tempMessage,
      }));

      clearAttachment();

      try {
        const uploadResult = await uploadFile(attachedImage, {
          location: "chat",
        });

        if (!uploadResult.success) {
          toast.error(uploadResult.error || "Failed to upload image");
          removeTempMessage(setTempMessages, tempMessageId);
          return;
        }

        const finalImageUrl = uploadResult.filename!;
        sendMessage(activeChatUser.id, finalImageUrl, MessageType.IMAGE);
        removeTempMessage(setTempMessages, tempMessageId);
      } catch (error) {
        toast.error("Failed to send image");
        console.error("Image upload error:", error);
        removeTempMessage(setTempMessages, tempMessageId);
      }
      return;
    }

    if (input.trim()) {
      const htmlContent = inputRef.current?.innerHTML || input;
      sendMessage(activeChatUser.id, htmlContent, MessageType.TEXT);
      setInput("");
      if (inputRef.current) {
        inputRef.current.innerHTML = "";
        inputRef.current.focus();
      }
    }
  }, [
    input,
    isConnected,
    activeChatUser,
    sendMessage,
    attachedImage,
    uploadFile,
    imagePreview,
  ]);

  const handleClose = useCallback(() => {
    setActiveChatUser(null);
  }, [setActiveChatUser]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  const handleGiftClick = useCallback(() => {
    setShowGiftsModal(true);
  }, []);

  const handleGiftSelect = useCallback(
    (gift: any) => {
      if (activeChatUser && isConnected) {
        const giftImageHtml = `<img src="${FormatS3ImgUrl(gift.image)}" alt="Gift" style="max-width: 100px; height: auto;" />`;
        sendMessage(activeChatUser.id, giftImageHtml, MessageType.GIFT);
        setShowGiftsModal(false);
      }
    },
    [activeChatUser, isConnected, sendMessage]
  );

  const handleEmojiClick = useCallback(() => {
    setShowEmojiDropdown(!showEmojiDropdown);
  }, [showEmojiDropdown]);

  const handleEmojiSelect = useCallback((emoji: any) => {
    const newInputContent = insertEmojiIntoInput(inputRef, emoji);
    if (newInputContent) {
      setInput(newInputContent);
    }
    setShowEmojiDropdown(false);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiDropdownRef.current &&
        !emojiDropdownRef.current.contains(event.target as Node)
      ) {
        setShowEmojiDropdown(false);
      }
    };

    if (showEmojiDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showEmojiDropdown]);

  const groupedMessages = useMemo(() => {
    const groups: Record<string, typeof currentMessages> = {};

    currentMessages.forEach((msg) => {
      const date = new Date(msg.timestamp).toISOString().split("T")[0];

      if (!groups[date]) groups[date] = [];
      groups[date].push(msg);
    });

    return groups;
  }, [currentMessages]);

  if (!activeChatUser) return null;

  return (
    <Card className="position-fixed chatpopup">
      <div className="d-flex justify-content-between align-items-center chatpopup-header">
        <div className="d-flex gap-2 align-items-center">
          <Image
            src={
              activeChatUser?.avatar
                ? FormatS3ImgUrl(activeChatUser.avatar)
                : defaultProfile
            }
            alt={activeChatUser.name}
            className="user-img object-fit-cover"
          />
          <div className="d-flex flex-column gap-1">
            <h6 className="name mb-0">{activeChatUser.name}</h6>
            <p className="location mb-0">{activeChatUser.location}</p>
            {/* <div
              className={`connection-status small ${isConnected ? "text-success" : "text-danger"}`}
            >
              {isConnected ? "● Online" : "● Offline"}
            </div> */}
          </div>
        </div>
        <CloseButton
          variant="black"
          className="close-btn"
          onClick={handleClose}
        />
      </div>
      <div className="message-screen" style={{ flex: 1 }}>
        {Object.entries(groupedMessages).map(([date, msgs]) => (
          <div key={date}>
            <div className="text-center text-muted small my-2">
              {getDateLabel(date)}
            </div>
            {msgs.map((msg, idx) => (
              <MessageItem key={msg.id || idx} msg={msg} idx={idx} />
            ))}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <ImagePreviewSection
        imagePreview={imagePreview}
        attachedImage={attachedImage}
      />

      <InputActions
        handleSend={handleSend}
        handleAttachClick={handleAttachClick}
        handleFileChange={handleFileChange}
        handleGiftClick={handleGiftClick}
        handleEmojiClick={handleEmojiClick}
        handleEmojiSelect={handleEmojiSelect}
        handleKeyDown={handleKeyDown}
        input={input}
        setInput={setInput}
        inputRef={inputRef}
        emojiDropdownRef={emojiDropdownRef}
        showEmojiDropdown={showEmojiDropdown}
        smileys={smileys}
        attachedImage={attachedImage}
        fileInputRef={fileInputRef}
        isConnected={isConnected}
      />

      <GiftsModal
        showGiftsModal={showGiftsModal}
        setShowGiftsModal={setShowGiftsModal}
        handleGiftSelect={handleGiftSelect}
        gifts={gifts}
      />
    </Card>
  );
};

export default ChatPopup;
