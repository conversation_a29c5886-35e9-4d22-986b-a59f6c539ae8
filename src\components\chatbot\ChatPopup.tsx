import { useChatConfig } from "@/hooks/useChatConfig";
import { useChatMessages } from "@/hooks/useChatMessages";
import { useImageAttachment } from "@/hooks/useImageAttachment";
import { useCallback } from "react";
import { Card } from "react-bootstrap";
import ChatHeader from "./ChatHeader";
import GiftsModal from "./GiftsModal";
import ImagePreviewSection from "./ImagePreviewSection";
import InputActions from "./InputActions";
import MessageList from "./MessageList";
import "./styles.scss";

/**
 * Main ChatPopup component - handles the chat interface
 * Refactored to use custom hooks and separated components
 */

const ChatPopup = () => {
  // Configuration and API hooks
  const {
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
    uploadFile,
    gifts,
    smileys,
  } = useChatConfig();

  // Image attachment functionality
  const {
    attachedImage,
    imagePreview,
    fileInputRef,
    handleAttachClick,
    handleFileChange,
    clearAttachment,
  } = useImageAttachment();

  // Chat messages and UI state management
  const {
    input,
    setInput,
    showGiftsModal,
    setShowGiftsModal,
    showEmojiDropdown,
    setShowEmojiDropdown,
    messagesEndRef,
    inputRef,
    emojiDropdownRef,
    groupedMessages,
    handleSend: baseSend,
    handleKeyDown,
    handleGiftSelect,
    handleEmojiSelect,
    handleGiftClick,
    handleEmojiClick,
  } = useChatMessages(
    activeChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
    uploadFile
  );

  // Enhanced handleSend that works with image attachments
  const handleSend = useCallback(() => {
    baseSend(attachedImage, imagePreview, clearAttachment);
  }, [baseSend, attachedImage, imagePreview, clearAttachment]);

  // Handle closing the chat popup
  const handleClose = useCallback(() => {
    setActiveChatUser(null);
  }, [setActiveChatUser]);

  // Don't render if no active chat user
  if (!activeChatUser) return null;

  return (
    <Card className="position-fixed chatpopup">
      <ChatHeader
        activeChatUser={activeChatUser}
        onClose={handleClose}
        isConnected={isConnected}
      />

      <MessageList
        groupedMessages={groupedMessages}
        messagesEndRef={messagesEndRef}
      />

      <ImagePreviewSection
        imagePreview={imagePreview}
        attachedImage={attachedImage}
      />

      <InputActions
        handleSend={handleSend}
        handleAttachClick={handleAttachClick}
        handleFileChange={handleFileChange}
        handleGiftClick={handleGiftClick}
        handleEmojiClick={handleEmojiClick}
        handleEmojiSelect={handleEmojiSelect}
        handleKeyDown={handleKeyDown}
        input={input}
        setInput={setInput}
        inputRef={inputRef}
        emojiDropdownRef={emojiDropdownRef}
        showEmojiDropdown={showEmojiDropdown}
        smileys={smileys}
        attachedImage={attachedImage}
        fileInputRef={fileInputRef}
        isConnected={isConnected}
      />

      <GiftsModal
        showGiftsModal={showGiftsModal}
        setShowGiftsModal={setShowGiftsModal}
        handleGiftSelect={handleGiftSelect}
        gifts={gifts}
      />
    </Card>
  );
};

export default ChatPopup;
