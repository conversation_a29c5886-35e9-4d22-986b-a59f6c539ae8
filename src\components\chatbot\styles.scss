@use "@/variables" as *;

.chatpopup {
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.15);
  width: 430px;
  right: 80px;
  bottom: 24px;
  border-radius: 25px;
  background: $white-color;
  z-index: 99999;

  &-header {
    border-radius: 25px 25px 0px 0px;
    background: $light-yellow;
    padding: 16px 20px;

    .user-img {
      width: 46px;
      height: 46px;
      border-radius: 46px;
      min-width: 46px;
    }

    .name {
      color: $black-color;
      font-size: 14px;
      font-weight: 600;
      line-height: 1.4;
      letter-spacing: 0.28px;
    }

    .location {
      color: $black-color;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.4;
      letter-spacing: 0.24px;
    }

    .close-btn {
      opacity: 1;
      &:focus {
        box-shadow: none;
      }
    }
  }

  .message-screen {
    padding: 16px 20px;
    background: $white-color;
    min-height: 400px;
    max-height: 400px;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;

    .message {
      font-size: 13px;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 8px;
      width: fit-content;
      max-width: 75%;
      padding: 10px 12px;
    }

    .message-send {
      border-radius: 16px 0px 16px 16px;
      background: $primary-color;
      color: $white-color;
    }

    .message-receive {
      border-radius: 0px 16px 16px 16px;
      border: 1px solid $light-gray-color;
      background: $white-color;
      color: $black-color;
    }
  }

  &-footer {
    border-radius: 0px 0px 25px 25px;
    border-top: 1px solid $light-gray-color;
    background: $white-color;
    padding: 12px 16px;

    .form-control {
      border: none;
      padding: 0px;
      color: $black-color;
      font-size: 14px;
      font-weight: 400;
      line-height: normal;
      &:focus {
        box-shadow: none;
      }

      // Placeholder for contentEditable using data-attribute
      &[data-placeholder]:not([data-has-value]) {
        position: relative;
      }
      &[data-placeholder]:not([data-has-value]):before {
        content: attr(data-placeholder);
        color: #9d9d9d;
        pointer-events: none;
        position: absolute;
        top: 0;
        left: 0px;
      }
    }

    .icons-btn {
      background: transparent;
      border: 0px;
      padding: 0px;
      min-width: auto;
    }

    .send-btn {
      border-radius: 25.441px;
      width: 32px;
      height: 32px;
      min-width: 32px;
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.emoji-dropdown {
  .emoji-item {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa !important;
    }
  }
}

.gift-item {
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.cursor-pointer {
  cursor: pointer !important;
}

.hover-bg-light:hover {
  background-color: #f8f9fa !important;
}

@media (max-width: 768px) {
  .chatpopup {
    width: 320px;
    right: 20px;
  }

  .emoji-dropdown {
    width: 250px !important;
  }
}
