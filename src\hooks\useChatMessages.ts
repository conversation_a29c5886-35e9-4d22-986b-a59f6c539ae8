import { CHAT_CONFIG } from "@/constants/chatConstants";
import { MessageType } from "@/types";
import { FormatS3ImgUrl } from "@/utils";
import {
  combineAndSortMessages,
  createTempMessage,
  groupMessagesByDate,
  insertEmojiIntoInput,
  removeTempMessage,
} from "@/utils/chatUtils";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import toast from "react-hot-toast";

/**
 * Custom hook for managing chat messages, sending, and UI interactions
 */
export const useChatMessages = (
  activeChatUser: any,
  messages: any,
  sendMessage: any,
  isConnected: boolean,
  getChatMessages: any,
  uploadFile: any
) => {
  // Local state
  const [input, setInput] = useState("");
  const [showGiftsModal, setShowGiftsModal] = useState(false);
  const [showEmojiDropdown, setShowEmojiDropdown] = useState(false);
  const [tempMessages, setTempMessages] = useState<Record<string, any>>({});

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const emojiDropdownRef = useRef<HTMLDivElement>(null);

  /**
   * Combines and sorts all messages (real + temporary)
   */
  const currentMessages = useMemo(() => {
    const baseMessages = activeChatUser ? messages[activeChatUser.id] || [] : [];
    return combineAndSortMessages(baseMessages, tempMessages);
  }, [activeChatUser, messages, tempMessages]);

  /**
   * Groups messages by date for display
   */
  const groupedMessages = useMemo(() => {
    return groupMessagesByDate(currentMessages);
  }, [currentMessages]);

  /**
   * Handles sending messages (text or image)
   */
  const handleSend = useCallback(
    async (attachedImage?: File | null, imagePreview?: string | null, clearAttachment?: () => void) => {
      if (!isConnected || !activeChatUser) return;

      if (attachedImage) {
        const tempMessageId = `${CHAT_CONFIG.TEMP_MESSAGE_PREFIX}${Date.now()}`;
        const tempMessage = createTempMessage(
          tempMessageId,
          activeChatUser.id,
          imagePreview || ""
        );

        setTempMessages((prev) => ({
          ...prev,
          [tempMessageId]: tempMessage,
        }));

        clearAttachment?.();

        try {
          const uploadResult = await uploadFile(attachedImage, {
            location: CHAT_CONFIG.UPLOAD_LOCATION,
          });

          if (!uploadResult.success) {
            toast.error(uploadResult.error || "Failed to upload image");
            removeTempMessage(setTempMessages, tempMessageId);
            return;
          }

          const finalImageUrl = uploadResult.filename!;
          sendMessage(activeChatUser.id, finalImageUrl, MessageType.IMAGE);
          removeTempMessage(setTempMessages, tempMessageId);
        } catch (error) {
          toast.error("Failed to send image");
          console.error("Image upload error:", error);
          removeTempMessage(setTempMessages, tempMessageId);
        }
        return;
      }

      if (input.trim()) {
        const htmlContent = inputRef.current?.innerHTML || input;
        sendMessage(activeChatUser.id, htmlContent, MessageType.TEXT);
        setInput("");
        if (inputRef.current) {
          inputRef.current.innerHTML = "";
          inputRef.current.focus();
        }
      }
    },
    [input, isConnected, activeChatUser, sendMessage, uploadFile]
  );

  /**
   * Handles keyboard events for message input
   */
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  /**
   * Handles gift selection and sending
   */
  const handleGiftSelect = useCallback(
    (gift: any) => {
      if (activeChatUser && isConnected) {
        const giftImageHtml = `<img src="${FormatS3ImgUrl(gift.image)}" alt="Gift" style="max-width: ${CHAT_CONFIG.GIFT_MAX_WIDTH}; height: auto;" />`;
        sendMessage(activeChatUser.id, giftImageHtml, MessageType.GIFT);
        setShowGiftsModal(false);
      }
    },
    [activeChatUser, isConnected, sendMessage]
  );

  /**
   * Handles emoji selection and insertion
   */
  const handleEmojiSelect = useCallback((emoji: any) => {
    const newInputContent = insertEmojiIntoInput(inputRef, emoji);
    if (newInputContent) {
      setInput(newInputContent);
    }
    setShowEmojiDropdown(false);
  }, []);

  // Effects
  useEffect(() => {
    if (activeChatUser && isConnected) {
      getChatMessages(activeChatUser.id);
    }
  }, [activeChatUser, isConnected, getChatMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentMessages]);

  useEffect(() => {
    const editableElement = inputRef.current;
    if (!editableElement) return;
    if (input.trim().length > 0) {
      editableElement.setAttribute("data-has-value", "true");
    } else {
      editableElement.removeAttribute("data-has-value");
    }
  }, [input]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiDropdownRef.current &&
        !emojiDropdownRef.current.contains(event.target as Node)
      ) {
        setShowEmojiDropdown(false);
      }
    };

    if (showEmojiDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showEmojiDropdown]);

  return {
    // State
    input,
    setInput,
    showGiftsModal,
    setShowGiftsModal,
    showEmojiDropdown,
    setShowEmojiDropdown,
    tempMessages,
    setTempMessages,

    // Refs
    messagesEndRef,
    inputRef,
    emojiDropdownRef,

    // Computed
    currentMessages,
    groupedMessages,

    // Handlers
    handleSend,
    handleKeyDown,
    handleGiftSelect,
    handleEmojiSelect,

    // Actions
    handleGiftClick: () => setShowGiftsModal(true),
    handleEmojiClick: () => setShowEmojiDropdown(!showEmojiDropdown),
  };
};
