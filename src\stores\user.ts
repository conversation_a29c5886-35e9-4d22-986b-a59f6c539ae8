import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  user: any | null;
  access_token: string | null;
}

interface PayloadInterface {
  userInfo: UserInfo;
  rememberMeInfo?: Record<string, string>;
  isAuthLoading?: boolean;
}

const initialState = {
  userInfo: {
    user: {} as any,
    access_token: "",
  },
  rememberMeInfo: {
    email: "",
  },
  isAuthLoading: false,
};

const userStore = (set: any) => ({
  ...initialState,
  setUserInfo: (data: UserInfo) =>
    set((state: PayloadInterface) => ({
      ...state,
      userInfo: data,
      isAuthLoading: false,
    })),
  setUserAvatar: (data: any) =>
    set((state: PayloadInterface) => ({
      ...state,
      userInfo: {
        ...state.userInfo,
        user: {
          ...state.userInfo.user,
          avatar: data,
        },
      },
    })),
  setUserDetails: (data: UserInfo) =>
    set((state: PayloadInterface) => ({
      ...state,
      userInfo: {
        ...state.userInfo,
        user: data,
      },
    })),
  setUserCoins: (totalCoins?: number, remainingCoins?: number) =>
    set((state: PayloadInterface) => ({
      ...state,
      userInfo: {
        ...state.userInfo,
        user: {
          ...state.userInfo.user,
          coins: totalCoins,
          remainingCoins,
        },
      },
    })),
  setRememberMeInfo: (data: Record<string, string>) =>
    set((state: PayloadInterface) => ({ ...state, rememberMeInfo: data })),
  setAuthLoading: (loading: boolean) =>
    set((state: PayloadInterface) => ({ ...state, isAuthLoading: loading })),
  resetUserState: () =>
    set((state: PayloadInterface) => ({
      ...initialState,
      rememberMeInfo: state.rememberMeInfo,
    })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    })
  )
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.access_token;
};

export const getUserCoins = () => {
  return {
    totalCoins: useUserStore.getState().userInfo.user.coins,
    remainingCoins: useUserStore.getState().userInfo.user.remainingCoin,
  };
};

export const {
  setUserInfo,
  resetUserState,
  setRememberMeInfo,
  setUserAvatar,
  setUserDetails,
  setAuthLoading,
  setUserCoins,
} = useUserStore.getState();

export default useUserStore;
