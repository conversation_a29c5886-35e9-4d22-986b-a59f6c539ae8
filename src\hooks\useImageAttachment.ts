import { validateImageFile } from "@/utils/imageValidation";
import { useCallback, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";

/**
 * Custom hook for handling image attachment functionality
 * Manages file selection, preview, validation, and cleanup
 */
export const useImageAttachment = () => {
  const [attachedImage, setAttachedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * Triggers the file input click
   */
  const handleAttachClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  /**
   * Handles file selection and validation
   */
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      const error = validateImageFile(file);
      if (error) {
        toast.error(error);
        return;
      }

      setAttachedImage(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    },
    []
  );

  /**
   * Removes the current attachment and cleans up preview URL
   */
  const handleRemoveAttachment = useCallback(() => {
    setAttachedImage(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [imagePreview]);

  /**
   * Clears attachment without URL cleanup (for after successful upload)
   */
  const clearAttachment = useCallback(() => {
    setAttachedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, []);

  /**
   * Cleanup effect to revoke object URLs on unmount
   */
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  return {
    attachedImage,
    imagePreview,
    fileInputRef,
    handleAttachClick,
    handleFileChange,
    handleRemoveAttachment,
    clearAttachment,
  };
};
