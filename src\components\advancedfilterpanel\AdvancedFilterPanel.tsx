import React, { useEffect, useState } from "react";
import { Offcanvas, Form, Button } from "react-bootstrap";
import "./styles.scss";

interface AdvancedFilterPanelProps {
  show: boolean;
  onClose: () => void;
  master: any;
  filters: Record<string, any>;
  setFilters: (
    filters:
      | Record<string, any>
      | ((prev: Record<string, any>) => Record<string, any>)
  ) => void;
  setParams: any;
}

const FILTER_CONFIG = [
  {
    label: "Relationship Status",
    filterKey: "relationshipStatusId",
    masterKey: "relationship_status",
    isMultiple: true,
  },
  {
    label: "Hair Color",
    filterKey: "hairColorId",
    masterKey: "hair_color",
    color: true,
    isMultiple: true,
  },
  {
    label: "Eye Color",
    filterKey: "eyeColorId",
    masterKey: "eye_color",
    color: true,
    isMultiple: true,
  },
  {
    label: "Best Feature",
    filterKey: "bestFeatureId",
    masterKey: "best_feature",
    isMultiple: true,
  },
  {
    label: "Personality",
    filterKey: "personalityId",
    master<PERSON>ey: "personality",
    isMultiple: true,
  },
  {
    label: "Appearance",
    filterKey: "appearanceId",
    masterKey: "appearance",
    isMultiple: true,
  },
  {
    label: "Body Type",
    filterKey: "bodyTypeId",
    masterKey: "body_type",
    isMultiple: true,
  },
  {
    label: "Smoking Habit",
    filterKey: "smokingHabitId",
    masterKey: "smoking_habits",
    isMultiple: true,
  },
  {
    label: "Drinking Habit",
    filterKey: "drinkingHabitId",
    masterKey: "drinking_habits",
    isMultiple: true,
  },
];

const ADVANCED_FILTER_KEYS = FILTER_CONFIG.map((f) => f.filterKey);

const getInitialLocalState = (filters: Record<string, any>) => {
  const state: Record<string, any> = {};
  for (const { filterKey, isMultiple } of FILTER_CONFIG) {
    if (isMultiple) {
      // For multi-select, convert comma-separated string to array of IDs
      const value = filters[filterKey];
      if (typeof value === 'string' && value) {
        state[filterKey] = value.split(',').filter(Boolean);
      } else {
        state[filterKey] = [];
      }
    } else {
      state[filterKey] = filters[filterKey] ?? undefined;
    }
  }
  return state;
};

const AdvancedFilterPanel: React.FC<AdvancedFilterPanelProps> = ({
  show,
  onClose,
  master,
  filters,
  setFilters,
  setParams,
}) => {
  const [localState, setLocalState] = useState(getInitialLocalState(filters));

  useEffect(() => {
    if (show) {
      setLocalState(getInitialLocalState(filters));
    }
  }, [show, filters]);

  const handleChange = (filterKey: string, value: any, isMultiple: boolean = false) => {
    if (isMultiple) {
      setLocalState((prev) => {
        const currentValues = prev[filterKey] || [];
        const optionId = value.id.toString();

        // Toggle the selection
        const updatedValues = currentValues.includes(optionId)
          ? currentValues.filter((id: string) => id !== optionId)
          : [...currentValues, optionId];

        return { ...prev, [filterKey]: updatedValues };
      });
    } else {
      setLocalState((prev) => ({ ...prev, [filterKey]: value }));
    }
  };

  const handleSubmit = () => {
    // For each advanced filter, store the appropriate format in global filters
    setFilters((prev: Record<string, any>) => {
      const updated: Record<string, any> = { ...prev };
      FILTER_CONFIG.forEach(({ filterKey, isMultiple }) => {
        if (isMultiple) {
          // For multi-select, store as comma-separated string of IDs
          const values = localState[filterKey] || [];
          updated[filterKey] = values.length > 0 ? values.join(',') : undefined;
        } else {
          // For single select, store the ID or value as before
          updated[filterKey] = localState[filterKey]?.id || localState[filterKey] || undefined;
        }
      });
      return updated;
    });

    setParams((prev: Record<string, any>) => {
      const updated: Record<string, any> = { ...prev };
      FILTER_CONFIG.forEach(({ filterKey, isMultiple }) => {
        if (isMultiple) {
          // For multi-select, store as comma-separated string of IDs
          const values = localState[filterKey] || [];
          updated[filterKey] = values.length > 0 ? values.join(',') : undefined;
        } else {
          // For single select, store the ID or value as before
          updated[filterKey] = localState[filterKey]?.id || localState[filterKey] || undefined;
        }
      });
      return updated;
    });
    onClose();
  };

  const handleReset = () => {
    const cleared: Record<string, any> = { ...filters };
    ADVANCED_FILTER_KEYS.forEach((key) => {
      cleared[key] = undefined;
    });
    setFilters(cleared);
    setLocalState(getInitialLocalState(cleared));
  };



  return (
    <Offcanvas
      show={show}
      onHide={onClose}
      placement="end"
      className="advance-filter-offcanvas"
    >
      <Offcanvas.Header closeButton>
        <Offcanvas.Title>Advanced Filters</Offcanvas.Title>
      </Offcanvas.Header>
      <Offcanvas.Body className="filter-sections-container">
        <div className="filter-sections">
          {FILTER_CONFIG.map((section) => {
            const options = master?.[section.masterKey] || [];
            const isColor = !!section.color;
            const isMultiple = !!section.isMultiple;

            // Get selected values based on selection type
            const getSelectedValues = () => {
              if (isMultiple) {
                return localState[section.filterKey] || [];
              } else {
                const selectedObj = options.find(
                  (opt: any) => localState[section.filterKey] === opt.id
                ) || localState[section.filterKey];
                return selectedObj;
              }
            };

            const selectedValues = getSelectedValues();

            return (
              <div className="filter-section" key={section.filterKey}>
                <h3 className="filter-section-title">{section.label}</h3>
                <Form.Group className="form-input pb-3">
                  {isColor ? (
                    <div className="filter-options-list d-flex flex-wrap gap-3 hair-color-selector">
                      {options.map((opt: any) => {
                        const isSelected = isMultiple
                          ? selectedValues.includes(opt.id.toString())
                          : selectedValues?.id === opt.id;

                        return (
                          <Form.Check
                            type={isMultiple ? "checkbox" : "radio"}
                            id={`${section.filterKey}-${opt.id}`}
                            name={isMultiple ? undefined : section.filterKey}
                            key={opt.id}
                            className={`hair-radio ps-0${isSelected ? " selected" : ""} pt-1 ps-1`}
                          >
                            <Form.Check.Input
                              type={isMultiple ? "checkbox" : "radio"}
                              id={`${section.filterKey}-${opt.id}`}
                              name={isMultiple ? undefined : section.filterKey}
                              value={opt.id}
                              checked={isSelected}
                              onChange={() =>
                                handleChange(section.filterKey, opt, isMultiple)
                              }
                              className="d-none"
                            />
                            <Form.Check.Label
                              className="hair-color-box"
                              htmlFor={`${section.filterKey}-${opt.id}`}
                            >
                              <span
                                className="color-outer-ring border-white"
                                style={{ borderColor: opt.colorcode || "#ccc" }}
                              >
                                <span
                                  className="color-inner-circle"
                                  style={{
                                    backgroundColor: opt.colorcode || "#ccc",
                                  }}
                                ></span>
                              </span>
                              <div className="color-label">{opt.title}</div>
                            </Form.Check.Label>
                          </Form.Check>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="filter-options-list d-flex flex-column gap-2">
                      {options.map((opt: any) => {
                        const isSelected = isMultiple
                          ? selectedValues.includes(opt.id.toString())
                          : selectedValues?.id === opt.id;

                        return (
                          <Form.Check
                            key={opt.id}
                            type={isMultiple ? "checkbox" : "radio"}
                            className="mb-1"
                          >
                            <Form.Check.Input
                              type={isMultiple ? "checkbox" : "radio"}
                              id={`${section.filterKey}-${opt.id}`}
                              name={isMultiple ? undefined : section.filterKey}
                              checked={isSelected}
                              onChange={() =>
                                handleChange(section.filterKey, opt, isMultiple)
                              }
                            />
                            <Form.Check.Label
                              htmlFor={`${section.filterKey}-${opt.id}`}
                              style={{ cursor: "pointer" }}
                            >
                              {opt.title}
                            </Form.Check.Label>
                          </Form.Check>
                        );
                      })}
                    </div>
                  )}
                </Form.Group>
              </div>
            );
          })}
        </div>
      </Offcanvas.Body>
      <div className="mt-auto d-flex justify-content-between gap-3 filter-btns">
        <Button
          variant="outline-secondary"
          className="w-50 first-btn"
          onClick={handleReset}
        >
          Clear
        </Button>
        <Button
          variant="primary"
          className="w-50 second-btn"
          onClick={handleSubmit}
        >
          Search
        </Button>
      </div>
    </Offcanvas>
  );
};

export default AdvancedFilterPanel;
