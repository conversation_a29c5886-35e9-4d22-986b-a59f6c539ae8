import { useTranslation } from "@/hooks/useTranslation";
import { ROUTE_PATH } from "@/routes";
import useUserStore from "@/stores/user";
import { IMAGE_PATHS } from "@/utils/image-path";
import { useState } from "react";
import { Button, Dropdown, Image, ProgressBar } from "react-bootstrap";
import { Link } from "react-router-dom";

const CoinsDropdown = () => {
  const { t } = useTranslation();
  const userData = useUserStore((state) => state.userInfo.user);
  const { coins = 0, remainingCoins = 0 } = userData;
  const [show, setShow] = useState(false);

  const onToggle = () => setShow(!show);

  return (
    <Dropdown
      align="end"
      className="coin-dropdown-wrapper"
      onToggle={onToggle}
      show={show}
    >
      <Dropdown.Toggle
        variant="light"
        className="d-flex align-items-center gap-2 bg-white rounded-pill px-3 py-2"
      >
        <Image src={IMAGE_PATHS.Coins} alt="" />
        {/* <span className="coins-number">{coins}</span> */}
      </Dropdown.Toggle>

      <Dropdown.Menu className="coin-details-dropdown p-0 border-0">
        <div className="p-3 coin-details-dropdown-header">
          <h6 className="title mb-0">{t("header.coinDetails")}</h6>
        </div>
        <div className="d-flex flex-column p-3">
          <div className="d-flex justify-content-between align-items-center balance-details">
            {/* <div className="d-flex flex-column gap-1 ">
              <div className="plan d-flex align-items-center gap-2">
                {t('header.basicPlan')}{" "}
                <Badge bg="success" pill>
                  {t('header.active')}
                </Badge>
              </div>
              <small className="balance">{t('header.accountBalance')}</small>
            </div> */}
            <div className="total-coins">
              {coins} {t("header.coins")}
            </div>
          </div>
          <div className="d-flex flex-column gap-2">
            <div className="remaining-coins">
              {remainingCoins} {t("header.coins")}{" "}
              <span>{t("header.remaining")}</span>
            </div>
            <ProgressBar
              now={(remainingCoins / coins) * 100}
              className="mb-3 custom-progress"
            />
          </div>
          <Link to={ROUTE_PATH.COINPURCHASE}>
            <Button
              variant="gradient-purple"
              className="d-flex justify-content-center align-items-center gap-2 plan-btn"
              onClick={() => setShow(false)}
            >
              {t("header.buyCoins")}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M17.6577 16.2427V6.34322M17.6577 6.34322H7.75818M17.6577 6.34322L6.34397 17.6569"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </Button>
          </Link>
        </div>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default CoinsDropdown;
