import { getDateLabel } from "@/utils/chatUtils";
import MessageItem from "./MessageItem";

interface MessageListProps {
  groupedMessages: Record<string, any[]>;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
}

/**
 * Component for rendering the list of messages grouped by date
 */
const MessageList = ({ groupedMessages, messagesEndRef }: MessageListProps) => {
  return (
    <div className="message-screen" style={{ flex: 1 }}>
      {Object.entries(groupedMessages).map(([date, msgs]) => (
        <div key={date}>
          <div className="text-center text-muted small my-2">
            {getDateLabel(date)}
          </div>
          {msgs.map((msg, idx) => (
            <MessageItem key={msg.id || idx} msg={msg} idx={idx} />
          ))}
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
