import { useMaster } from "@/api/utils.api";
import { useS3Upload } from "@/hooks/useS3Upload";
import useChatStore from "@/stores/useChatStore";

/**
 * Custom hook that provides all chat-related configuration and API methods
 * Centralizes chat store, S3 upload, and master data access
 */
export const useChatConfig = () => {
  // Store hooks
  const {
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
  } = useChatStore();

  // API hooks
  const { uploadFile } = useS3Upload();
  const { data: master = {} } = useMaster();
  const { gifts = [], smiley: smileys = [] } = master;

  return {
    // Chat store
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,

    // S3 upload
    uploadFile,

    // Master data
    gifts,
    smileys,
  };
};