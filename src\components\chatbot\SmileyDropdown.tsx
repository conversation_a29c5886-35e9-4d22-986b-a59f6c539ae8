import { FormatS3ImgUrl } from "@/utils";
import { Image } from "react-bootstrap";

interface Smiley {
  id: number;
  image: string;
  group: string;
}
interface SmileyDropdownProps {
  smileys: Smiley[];
  handleEmojiSelect: (emoji: any) => void;
}

const SmileyDropdown = ({
  smileys,
  handleEmojiSelect,
}: SmileyDropdownProps) => {
  return (
    <div
      className="emoji-dropdown position-absolute bottom-100 mb-2 bg-white border rounded shadow-lg p-3"
      style={{
        width: "300px",
        maxHeight: "200px",
        overflowY: "auto",
        zIndex: 1000,
        right: "-4rem",
      }}
    >
      <div className="d-flex flex-wrap gap-2">
        {smileys.length > 0 ? (
          smileys.map((emoji: Smiley) => (
            <div
              key={emoji.id}
              className="emoji-item p-1 rounded cursor-pointer hover-bg-light"
              onClick={() => handleEmojiSelect(emoji)}
              style={{ cursor: "pointer" }}
              title={emoji?.group}
            >
              <Image
                src={FormatS3ImgUrl(emoji.image)}
                alt="Emoji"
                style={{ width: "30px", height: "30px" }}
              />
            </div>
          ))
        ) : (
          <div className="text-muted small">No emojis available</div>
        )}
      </div>
    </div>
  );
};

export default SmileyDropdown;
