import { SearchNormal1 } from "iconsax-react";
import React, { FC } from "react";
import { Form, InputGroup } from "react-bootstrap";

interface FilterByNameProps {
  onChangeFilter: (key: string, value: string) => void;
  value: string;
  onSearch: () => void;
}

const FilterByName: FC<FilterByNameProps> = ({
  onChangeFilter,
  value,
  onSearch,
}) => {
  return (
    <InputGroup className="search-input form-input">
      <InputGroup.Text className=" border-0 p-0">
        <SearchNormal1 size="16" color="#141414" />
      </InputGroup.Text>
      <Form.Control
        type="text"
        placeholder="Search by name or location"
        className="border-0 p-0"
        value={value}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          onChangeFilter("name", e.target.value)
        }
        onKeyDown={(e: React.KeyboardEvent) => {
          if (e.key === "Enter") {
            onSearch();
          }
        }}
      />
    </InputGroup>
  );
};

export default FilterByName;
