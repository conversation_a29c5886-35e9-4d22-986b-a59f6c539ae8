import { IMAGE_STYLES, LOADING_OVERLAY_STYLES } from "@/constants/chatConstants";
import { MessageType } from "@/types";
import { FormatS3ImgUrl } from "@/utils";
import parse from "html-react-parser";

const MessageItem = ({ msg, idx }: { msg: any; idx: number }) => {
  const isTemp = msg.isTemp || false;
  const tempId = msg.tempId || null;

  return (
    <div
      key={msg.id || idx}
      className={`message ${
        msg.sender === "me"
          ? "message-send ms-auto text-start"
          : "message-receive text-start me-auto"
      }`}
    >
      {msg.messageType === MessageType.IMAGE ? (
        <div
          className="image-message-wrapper position-relative"
          data-temp-id={tempId}
        >
          {isTemp && (
            <div
              className="image-skeleton position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
              style={LOADING_OVERLAY_STYLES}
            >
              <div
                className="spinner-border spinner-border-sm text-dark"
                role="status"
              >
                <span className="visually-hidden">Uploading...</span>
              </div>
            </div>
          )}
          <img
            src={isTemp ? msg.message : FormatS3ImgUrl(msg.message)}
            alt="Attached Image"
            style={{
              ...IMAGE_STYLES,
              opacity: isTemp ? 0.7 : 1,
            }}
          />
        </div>
      ) : (
        parse(msg.message)
      )}
    </div>
  );
};

export default MessageItem;
