import defaultProfile from "@/assets/images/user.png";
import { FormatS3ImgUrl } from "@/utils";
import { CloseButton, Image } from "react-bootstrap";

interface ChatHeaderProps {
  activeChatUser: {
    id: number;
    name: string;
    location: string;
    avatar?: string;
  };
  onClose: () => void;
  isConnected?: boolean;
}

/**
 * Chat header component displaying user info and close button
 */
const ChatHeader = ({ activeChatUser, onClose, isConnected }: ChatHeaderProps) => {
  return (
    <div className="d-flex justify-content-between align-items-center chatpopup-header">
      <div className="d-flex gap-2 align-items-center">
        <Image
          src={
            activeChatUser?.avatar
              ? FormatS3ImgUrl(activeChatUser.avatar)
              : defaultProfile
          }
          alt={activeChatUser.name}
          className="user-img object-fit-cover"
        />
        <div className="d-flex flex-column gap-1">
          <h6 className="name mb-0">{activeChatUser.name}</h6>
          <p className="location mb-0">{activeChatUser.location}</p>
          {/* Connection status can be enabled if needed */}
          {/* <div
            className={`connection-status small ${isConnected ? "text-success" : "text-danger"}`}
          >
            {isConnected ? "● Online" : "● Offline"}
          </div> */}
        </div>
      </div>
      <CloseButton
        variant="black"
        className="close-btn"
        onClick={onClose}
      />
    </div>
  );
};

export default ChatHeader;
