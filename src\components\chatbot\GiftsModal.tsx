import { FormatS3ImgUrl } from "@/utils";
import { Col, Image, Modal, Row } from "react-bootstrap";

interface Gift {
  id: number;
  image: string;
  group: string;
}

interface GiftsModalProps {
  showGiftsModal: boolean;
  setShowGiftsModal: (show: boolean) => void;
  handleGiftSelect: (gift: any) => void;
  gifts: Gift[];
}

const GiftsModal = ({
  showGiftsModal,
  setShowGiftsModal,
  handleGiftSelect,
  gifts,
}: GiftsModalProps) => {
  return (
    <Modal
      show={showGiftsModal}
      onHide={() => setShowGiftsModal(false)}
      size="lg"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title>Select a Gift</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Row>
          {gifts.length > 0 ? (
            gifts.map((gift: Gift) => (
              <Col xs={6} md={4} lg={3} key={gift.id} className="mb-3">
                <div
                  className="gift-item text-center p-2 border rounded cursor-pointer hover-bg-light"
                  onClick={() => handleGiftSelect(gift)}
                  style={{ cursor: "pointer" }}
                  title={gift?.group}
                >
                  <Image
                    src={FormatS3ImgUrl(gift.image)}
                    alt="Gift"
                    fluid
                    style={{ maxHeight: "80px", objectFit: "contain" }}
                  />
                  {/* <div className="small text-muted mt-1">{gift.group}</div> */}
                </div>
              </Col>
            ))
          ) : (
            <Col xs={12} className="text-center py-4">
              <div className="text-muted">No gifts available</div>
            </Col>
          )}
        </Row>
      </Modal.Body>
    </Modal>
  );
};

export default GiftsModal;
