/**
 * Constants used across chat components
 */

// Image styling constants
export const IMAGE_STYLES = {
  maxWidth: "200px",
  height: "auto",
  borderRadius: "8px",
} as const;

export const LOADING_OVERLAY_STYLES = {
  background: "rgba(240, 240, 240, 0.9)",
  borderRadius: "8px",
  zIndex: 1,
} as const;

export const PREVIEW_IMAGE_STYLES = {
  maxWidth: "100px",
  maxHeight: "100px",
  objectFit: "cover" as const,
  borderRadius: "8px",
};

export const EMOJI_STYLES = {
  maxWidth: "30px",
  height: "auto",
};

// Input styling constants
export const INPUT_STYLES = {
  height: "max-content",
  maxHeight: "57px",
  overflowY: "auto" as const,
  whiteSpace: "pre-wrap" as const,
  wordBreak: "break-word" as const,
  outline: "none",
};

// Chat configuration
export const CHAT_CONFIG = {
  UPLOAD_LOCATION: "chat",
  GIFT_MAX_WIDTH: "100px",
  TEMP_MESSAGE_PREFIX: "temp-",
} as const;
